-- ChatBot App Database Schema
-- <PERSON><PERSON>y script này trong Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends auth.users)
CREATE TABLE IF NOT EXISTS users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    username TEXT UNIQUE NOT NULL,
    avatar TEXT,
    balance DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bots table
CREATE TABLE IF NOT EXISTS bots (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    token TEXT UNIQUE NOT NULL,
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    avatar TEXT,
    is_active BOOLEAN DEFAULT true,
    pricing JSONB NOT NULL DEFAULT '{"message_cost": 0.01, "command_cost": 0.05, "file_upload_cost": 0.1}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bot commands table
CREATE TABLE IF NOT EXISTS bot_commands (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    bot_id UUID REFERENCES bots(id) ON DELETE CASCADE NOT NULL,
    command TEXT NOT NULL,
    description TEXT NOT NULL,
    response_type TEXT CHECK (response_type IN ('text', 'image', 'file', 'code')) DEFAULT 'text',
    response_content TEXT NOT NULL,
    price_per_use DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(bot_id, command)
);

-- Chats table
CREATE TABLE IF NOT EXISTS chats (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT CHECK (type IN ('direct', 'group', 'bot')) NOT NULL,
    bot_id UUID REFERENCES bots(id) ON DELETE SET NULL,
    created_by UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat participants table
CREATE TABLE IF NOT EXISTS chat_participants (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chat_id UUID REFERENCES chats(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(chat_id, user_id)
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chat_id UUID REFERENCES chats(id) ON DELETE CASCADE NOT NULL,
    sender_id UUID NOT NULL, -- Can be user_id or bot_id
    sender_type TEXT CHECK (sender_type IN ('user', 'bot')) NOT NULL,
    content TEXT NOT NULL,
    message_type TEXT CHECK (message_type IN ('text', 'image', 'file', 'code', 'system')) DEFAULT 'text',
    file_url TEXT,
    file_name TEXT,
    file_size INTEGER,
    cost DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    type TEXT CHECK (type IN ('deposit', 'withdrawal', 'bot_usage', 'bot_earning')) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT NOT NULL,
    bot_id UUID REFERENCES bots(id) ON DELETE SET NULL,
    message_id UUID REFERENCES messages(id) ON DELETE SET NULL,
    status TEXT CHECK (status IN ('pending', 'completed', 'failed')) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Code executions table
CREATE TABLE IF NOT EXISTS code_executions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
    bot_id UUID REFERENCES bots(id) ON DELETE CASCADE NOT NULL,
    code TEXT NOT NULL,
    language TEXT NOT NULL,
    output TEXT,
    error TEXT,
    execution_time INTEGER, -- in milliseconds
    cost DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_messages_chat_id ON messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_chat_participants_user_id ON chat_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_participants_chat_id ON chat_participants(chat_id);
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_bots_owner_id ON bots(owner_id);
CREATE INDEX IF NOT EXISTS idx_bot_commands_bot_id ON bot_commands(bot_id);
CREATE INDEX IF NOT EXISTS idx_code_executions_user_id ON code_executions(user_id);

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE bots ENABLE ROW LEVEL SECURITY;
ALTER TABLE bot_commands ENABLE ROW LEVEL SECURITY;
ALTER TABLE chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE code_executions ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- Bots policies
CREATE POLICY "Anyone can view active bots" ON bots FOR SELECT USING (is_active = true);
CREATE POLICY "Users can manage own bots" ON bots FOR ALL USING (auth.uid() = owner_id);

-- Bot commands policies
CREATE POLICY "Anyone can view bot commands" ON bot_commands FOR SELECT USING (
    EXISTS (SELECT 1 FROM bots WHERE bots.id = bot_commands.bot_id AND bots.is_active = true)
);
CREATE POLICY "Bot owners can manage commands" ON bot_commands FOR ALL USING (
    EXISTS (SELECT 1 FROM bots WHERE bots.id = bot_commands.bot_id AND bots.owner_id = auth.uid())
);

-- Chats policies
CREATE POLICY "Users can view chats they participate in" ON chats FOR SELECT USING (
    EXISTS (SELECT 1 FROM chat_participants WHERE chat_participants.chat_id = chats.id AND chat_participants.user_id = auth.uid())
);
CREATE POLICY "Users can create chats" ON chats FOR INSERT WITH CHECK (auth.uid() = created_by);
CREATE POLICY "Chat creators can update chats" ON chats FOR UPDATE USING (auth.uid() = created_by);

-- Chat participants policies
CREATE POLICY "Users can view chat participants for their chats" ON chat_participants FOR SELECT USING (
    EXISTS (SELECT 1 FROM chat_participants cp WHERE cp.chat_id = chat_participants.chat_id AND cp.user_id = auth.uid())
);
CREATE POLICY "Users can join chats" ON chat_participants FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Messages policies
CREATE POLICY "Users can view messages in their chats" ON messages FOR SELECT USING (
    EXISTS (SELECT 1 FROM chat_participants WHERE chat_participants.chat_id = messages.chat_id AND chat_participants.user_id = auth.uid())
);
CREATE POLICY "Users can send messages to their chats" ON messages FOR INSERT WITH CHECK (
    EXISTS (SELECT 1 FROM chat_participants WHERE chat_participants.chat_id = messages.chat_id AND chat_participants.user_id = auth.uid())
    AND (sender_type = 'user' AND sender_id::text = auth.uid()::text)
);

-- Transactions policies
CREATE POLICY "Users can view own transactions" ON transactions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can create transactions" ON transactions FOR INSERT WITH CHECK (true);

-- Code executions policies
CREATE POLICY "Users can view own code executions" ON code_executions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create code executions" ON code_executions FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Functions and triggers

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bots_updated_at BEFORE UPDATE ON bots FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chats_updated_at BEFORE UPDATE ON chats FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO users (id, email, username)
    VALUES (NEW.id, NEW.email, COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)));
    RETURN NEW;
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Sample data (optional)
-- Uncomment to insert sample data

/*
-- Insert sample bot
INSERT INTO bots (name, description, token, owner_id, pricing) VALUES 
('Assistant Bot', 'AI assistant bot that can help with various tasks', 'bot_sample_token_123', (SELECT id FROM users LIMIT 1), '{"message_cost": 0.01, "command_cost": 0.05, "file_upload_cost": 0.1}');

-- Insert sample bot commands
INSERT INTO bot_commands (bot_id, command, description, response_type, response_content, price_per_use) VALUES
((SELECT id FROM bots WHERE name = 'Assistant Bot'), '/help', 'Show available commands', 'text', 'Available commands:\n/help - Show this help\n/weather - Get weather info\n/joke - Tell a joke', 0),
((SELECT id FROM bots WHERE name = 'Assistant Bot'), '/weather', 'Get weather information', 'text', 'Today is sunny with 25°C temperature. Perfect weather for outdoor activities!', 0.02),
((SELECT id FROM bots WHERE name = 'Assistant Bot'), '/joke', 'Tell a random joke', 'text', 'Why do programmers prefer dark mode? Because light attracts bugs! 🐛', 0.01);
*/
