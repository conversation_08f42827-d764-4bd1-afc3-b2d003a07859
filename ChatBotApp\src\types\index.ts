// Types cho ứng dụng Chat Bot
export interface User {
  id: string;
  email: string;
  username: string;
  avatar?: string;
  balance: number;
  created_at: string;
  updated_at: string;
}

export interface Bot {
  id: string;
  name: string;
  description: string;
  token: string;
  owner_id: string;
  avatar?: string;
  is_active: boolean;
  commands: BotCommand[];
  pricing: BotPricing;
  created_at: string;
  updated_at: string;
}

export interface BotCommand {
  id: string;
  bot_id: string;
  command: string;
  description: string;
  response_type: 'text' | 'image' | 'file' | 'code';
  response_content: string;
  price_per_use: number;
  is_active: boolean;
}

export interface BotPricing {
  message_cost: number;
  command_cost: number;
  file_upload_cost: number;
}

export interface Chat {
  id: string;
  name: string;
  type: 'direct' | 'group' | 'bot';
  participants: string[];
  bot_id?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  last_message?: Message;
}

export interface Message {
  id: string;
  chat_id: string;
  sender_id: string;
  sender_type: 'user' | 'bot';
  content: string;
  message_type: 'text' | 'image' | 'file' | 'code' | 'system';
  file_url?: string;
  file_name?: string;
  file_size?: number;
  cost?: number;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: string;
  user_id: string;
  type: 'deposit' | 'withdrawal' | 'bot_usage' | 'bot_earning';
  amount: number;
  description: string;
  bot_id?: string;
  message_id?: string;
  status: 'pending' | 'completed' | 'failed';
  created_at: string;
}

export interface CodeExecution {
  id: string;
  user_id: string;
  bot_id: string;
  code: string;
  language: string;
  output: string;
  error?: string;
  execution_time: number;
  cost: number;
  created_at: string;
}

// Navigation types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  Register: undefined;
  ChatList: undefined;
  Chat: { chatId: string };
  BotManagement: undefined;
  CreateBot: undefined;
  EditBot: { botId: string };
  Profile: undefined;
  Wallet: undefined;
  Settings: undefined;
};

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Chat message for react-native-gifted-chat
export interface GiftedChatMessage {
  _id: string | number;
  text: string;
  createdAt: Date;
  user: {
    _id: string | number;
    name: string;
    avatar?: string;
  };
  image?: string;
  video?: string;
  audio?: string;
  system?: boolean;
  sent?: boolean;
  received?: boolean;
  pending?: boolean;
  quickReplies?: {
    type: 'radio' | 'checkbox';
    keepIt?: boolean;
    values: Array<{
      title: string;
      value: string;
      messageId?: string | number;
    }>;
  };
}
