import { create } from 'zustand';
import { Bot, BotCommand, Transaction } from '../types';
import { supabase, TABLES } from '../config/supabase';
import { v4 as uuidv4 } from 'react-native-uuid';

interface BotState {
  bots: Bot[];
  userBots: Bot[];
  currentBot: Bot | null;
  botCommands: BotCommand[];
  isLoading: boolean;
  
  // Actions
  loadBots: () => Promise<void>;
  loadUserBots: (userId: string) => Promise<void>;
  loadBotCommands: (botId: string) => Promise<void>;
  createBot: (botData: Partial<Bot>) => Promise<{ success: boolean; bot?: Bot; error?: string }>;
  updateBot: (botId: string, updates: Partial<Bot>) => Promise<{ success: boolean; error?: string }>;
  deleteBot: (botId: string) => Promise<{ success: boolean; error?: string }>;
  createBotCommand: (commandData: Partial<BotCommand>) => Promise<{ success: boolean; error?: string }>;
  updateBotCommand: (commandId: string, updates: Partial<BotCommand>) => Promise<{ success: boolean; error?: string }>;
  deleteBotCommand: (commandId: string) => Promise<{ success: boolean; error?: string }>;
  generateBotToken: () => string;
  setCurrentBot: (bot: Bot | null) => void;
  executeCode: (code: string, language: string, botId: string, userId: string) => Promise<{ success: boolean; output?: string; error?: string }>;
}

export const useBotStore = create<BotState>((set, get) => ({
  bots: [],
  userBots: [],
  currentBot: null,
  botCommands: [],
  isLoading: false,

  loadBots: async () => {
    try {
      set({ isLoading: true });

      const { data: bots, error } = await supabase
        .from(TABLES.BOTS)
        .select(`
          *,
          owner:users(id, username, avatar)
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      set({ bots: bots || [], isLoading: false });
    } catch (error) {
      console.error('Load bots error:', error);
      set({ isLoading: false });
    }
  },

  loadUserBots: async (userId: string) => {
    try {
      set({ isLoading: true });

      const { data: userBots, error } = await supabase
        .from(TABLES.BOTS)
        .select('*')
        .eq('owner_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      set({ userBots: userBots || [], isLoading: false });
    } catch (error) {
      console.error('Load user bots error:', error);
      set({ isLoading: false });
    }
  },

  loadBotCommands: async (botId: string) => {
    try {
      const { data: commands, error } = await supabase
        .from(TABLES.BOT_COMMANDS)
        .select('*')
        .eq('bot_id', botId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      set({ botCommands: commands || [] });
    } catch (error) {
      console.error('Load bot commands error:', error);
    }
  },

  createBot: async (botData: Partial<Bot>) => {
    try {
      const botId = uuidv4();
      const token = get().generateBotToken();
      const now = new Date().toISOString();

      const { data: bot, error } = await supabase
        .from(TABLES.BOTS)
        .insert({
          id: botId,
          ...botData,
          token,
          is_active: true,
          pricing: botData.pricing || {
            message_cost: 0.01,
            command_cost: 0.05,
            file_upload_cost: 0.1,
          },
          created_at: now,
          updated_at: now,
        })
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      // Tạo một số lệnh mặc định
      const defaultCommands = [
        {
          id: uuidv4(),
          bot_id: botId,
          command: '/help',
          description: 'Hiển thị danh sách lệnh',
          response_type: 'text' as const,
          response_content: 'Danh sách lệnh có sẵn:\n/help - Hiển thị trợ giúp\n/info - Thông tin bot\n/ping - Kiểm tra kết nối',
          price_per_use: 0,
          is_active: true,
        },
        {
          id: uuidv4(),
          bot_id: botId,
          command: '/info',
          description: 'Thông tin về bot',
          response_type: 'text' as const,
          response_content: `Tôi là ${botData.name}. ${botData.description}`,
          price_per_use: 0,
          is_active: true,
        },
        {
          id: uuidv4(),
          bot_id: botId,
          command: '/ping',
          description: 'Kiểm tra kết nối',
          response_type: 'text' as const,
          response_content: 'Pong! Bot đang hoạt động bình thường.',
          price_per_use: 0,
          is_active: true,
        },
      ];

      await supabase
        .from(TABLES.BOT_COMMANDS)
        .insert(defaultCommands);

      set(state => ({
        userBots: [bot, ...state.userBots],
        bots: [bot, ...state.bots],
      }));

      return { success: true, bot };
    } catch (error) {
      console.error('Create bot error:', error);
      return { success: false, error: 'Có lỗi xảy ra khi tạo bot' };
    }
  },

  updateBot: async (botId: string, updates: Partial<Bot>) => {
    try {
      const { data: bot, error } = await supabase
        .from(TABLES.BOTS)
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', botId)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      set(state => ({
        userBots: state.userBots.map(b => b.id === botId ? bot : b),
        bots: state.bots.map(b => b.id === botId ? bot : b),
        currentBot: state.currentBot?.id === botId ? bot : state.currentBot,
      }));

      return { success: true };
    } catch (error) {
      console.error('Update bot error:', error);
      return { success: false, error: 'Có lỗi xảy ra khi cập nhật bot' };
    }
  },

  deleteBot: async (botId: string) => {
    try {
      const { error } = await supabase
        .from(TABLES.BOTS)
        .delete()
        .eq('id', botId);

      if (error) {
        return { success: false, error: error.message };
      }

      set(state => ({
        userBots: state.userBots.filter(b => b.id !== botId),
        bots: state.bots.filter(b => b.id !== botId),
        currentBot: state.currentBot?.id === botId ? null : state.currentBot,
      }));

      return { success: true };
    } catch (error) {
      console.error('Delete bot error:', error);
      return { success: false, error: 'Có lỗi xảy ra khi xóa bot' };
    }
  },

  createBotCommand: async (commandData: Partial<BotCommand>) => {
    try {
      const commandId = uuidv4();

      const { data: command, error } = await supabase
        .from(TABLES.BOT_COMMANDS)
        .insert({
          id: commandId,
          ...commandData,
          is_active: true,
        })
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      set(state => ({
        botCommands: [command, ...state.botCommands],
      }));

      return { success: true };
    } catch (error) {
      console.error('Create bot command error:', error);
      return { success: false, error: 'Có lỗi xảy ra khi tạo lệnh' };
    }
  },

  updateBotCommand: async (commandId: string, updates: Partial<BotCommand>) => {
    try {
      const { data: command, error } = await supabase
        .from(TABLES.BOT_COMMANDS)
        .update(updates)
        .eq('id', commandId)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      set(state => ({
        botCommands: state.botCommands.map(c => c.id === commandId ? command : c),
      }));

      return { success: true };
    } catch (error) {
      console.error('Update bot command error:', error);
      return { success: false, error: 'Có lỗi xảy ra khi cập nhật lệnh' };
    }
  },

  deleteBotCommand: async (commandId: string) => {
    try {
      const { error } = await supabase
        .from(TABLES.BOT_COMMANDS)
        .delete()
        .eq('id', commandId);

      if (error) {
        return { success: false, error: error.message };
      }

      set(state => ({
        botCommands: state.botCommands.filter(c => c.id !== commandId),
      }));

      return { success: true };
    } catch (error) {
      console.error('Delete bot command error:', error);
      return { success: false, error: 'Có lỗi xảy ra khi xóa lệnh' };
    }
  },

  generateBotToken: () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let token = '';
    for (let i = 0; i < 32; i++) {
      token += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return `bot_${token}`;
  },

  setCurrentBot: (bot: Bot | null) => {
    set({ currentBot: bot });
  },

  executeCode: async (code: string, language: string, botId: string, userId: string) => {
    try {
      // Đây là mock implementation - trong thực tế bạn sẽ cần một service để execute code
      // Có thể sử dụng các service như Judge0, CodeX, hoặc tự build
      
      const executionId = uuidv4();
      const startTime = Date.now();
      
      // Mock execution - thay thế bằng API thực tế
      let output = '';
      let error = '';
      
      try {
        // Đây chỉ là mock - không thực sự execute code
        if (language === 'javascript') {
          output = `// Code executed successfully\n// Input: ${code}\n// Output: Mock result for JavaScript`;
        } else if (language === 'python') {
          output = `# Code executed successfully\n# Input: ${code}\n# Output: Mock result for Python`;
        } else {
          output = `Code executed successfully\nInput: ${code}\nOutput: Mock result for ${language}`;
        }
      } catch (e) {
        error = `Execution error: ${e}`;
      }
      
      const executionTime = Date.now() - startTime;
      const cost = 0.1; // Cost per execution
      
      // Lưu kết quả execution
      await supabase
        .from(TABLES.CODE_EXECUTIONS)
        .insert({
          id: executionId,
          user_id: userId,
          bot_id: botId,
          code,
          language,
          output,
          error: error || null,
          execution_time: executionTime,
          cost,
          created_at: new Date().toISOString(),
        });
      
      // Trừ tiền user
      const { data: user } = await supabase
        .from('users')
        .select('balance')
        .eq('id', userId)
        .single();
      
      if (user && user.balance >= cost) {
        await supabase
          .from('users')
          .update({ balance: user.balance - cost })
          .eq('id', userId);
        
        // Ghi transaction
        await supabase
          .from(TABLES.TRANSACTIONS)
          .insert({
            id: uuidv4(),
            user_id: userId,
            type: 'bot_usage',
            amount: -cost,
            description: `Code execution - ${language}`,
            bot_id: botId,
            status: 'completed',
            created_at: new Date().toISOString(),
          });
      }
      
      return { 
        success: true, 
        output: error ? `Error: ${error}` : output 
      };
    } catch (error) {
      console.error('Execute code error:', error);
      return { 
        success: false, 
        error: 'Có lỗi xảy ra khi thực thi code' 
      };
    }
  },
}));
