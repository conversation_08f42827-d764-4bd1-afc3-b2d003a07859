{"name": "chatbotapp", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/native": "^7.1.9", "@supabase/supabase-js": "^2.49.8", "date-fns": "^4.1.0", "expo": "~53.0.9", "expo-crypto": "^14.1.4", "expo-document-picker": "^13.1.5", "expo-image-picker": "^16.1.4", "expo-router": "^5.0.7", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-gifted-chat": "^2.8.1", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-url-polyfill": "^2.0.0", "react-native-uuid": "^2.0.3", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}