import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
  ScrollView,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuthStore } from '../store/authStore';
import { useBotStore } from '../store/botStore';
import { Bot, BotCommand } from '../types';

interface BotManagementScreenProps {
  onBack: () => void;
}

export default function BotManagementScreen({ onBack }: BotManagementScreenProps) {
  const { user } = useAuthStore();
  const { 
    userBots, 
    currentBot,
    botCommands,
    loadUserBots, 
    loadBotCommands,
    createBot, 
    updateBot, 
    deleteBot,
    createBotCommand,
    updateBotCommand,
    deleteBotCommand,
    setCurrentBot,
    isLoading 
  } = useBotStore();

  const [refreshing, setRefreshing] = useState(false);
  const [showCreateBotModal, setShowCreateBotModal] = useState(false);
  const [showEditBotModal, setShowEditBotModal] = useState(false);
  const [showCommandModal, setShowCommandModal] = useState(false);
  const [editingCommand, setEditingCommand] = useState<BotCommand | null>(null);

  // Bot form state
  const [botName, setBotName] = useState('');
  const [botDescription, setBotDescription] = useState('');
  const [messageCost, setMessageCost] = useState('0.01');
  const [commandCost, setCommandCost] = useState('0.05');
  const [fileUploadCost, setFileUploadCost] = useState('0.1');

  // Command form state
  const [commandName, setCommandName] = useState('');
  const [commandDescription, setCommandDescription] = useState('');
  const [commandResponse, setCommandResponse] = useState('');
  const [commandPrice, setCommandPrice] = useState('0');
  const [commandType, setCommandType] = useState<'text' | 'image' | 'file' | 'code'>('text');
  const [commandActive, setCommandActive] = useState(true);

  useEffect(() => {
    if (user) {
      loadUserBots(user.id);
    }
  }, [user]);

  const handleRefresh = async () => {
    if (!user) return;
    setRefreshing(true);
    await loadUserBots(user.id);
    setRefreshing(false);
  };

  const resetBotForm = () => {
    setBotName('');
    setBotDescription('');
    setMessageCost('0.01');
    setCommandCost('0.05');
    setFileUploadCost('0.1');
  };

  const resetCommandForm = () => {
    setCommandName('');
    setCommandDescription('');
    setCommandResponse('');
    setCommandPrice('0');
    setCommandType('text');
    setCommandActive(true);
    setEditingCommand(null);
  };

  const handleCreateBot = async () => {
    if (!user || !botName.trim() || !botDescription.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập đầy đủ thông tin');
      return;
    }

    const result = await createBot({
      name: botName.trim(),
      description: botDescription.trim(),
      owner_id: user.id,
      pricing: {
        message_cost: parseFloat(messageCost),
        command_cost: parseFloat(commandCost),
        file_upload_cost: parseFloat(fileUploadCost),
      },
    });

    if (result.success) {
      setShowCreateBotModal(false);
      resetBotForm();
      Alert.alert('Thành công', 'Bot đã được tạo thành công!');
    } else {
      Alert.alert('Lỗi', result.error || 'Không thể tạo bot');
    }
  };

  const handleEditBot = async () => {
    if (!currentBot || !botName.trim() || !botDescription.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập đầy đủ thông tin');
      return;
    }

    const result = await updateBot(currentBot.id, {
      name: botName.trim(),
      description: botDescription.trim(),
      pricing: {
        message_cost: parseFloat(messageCost),
        command_cost: parseFloat(commandCost),
        file_upload_cost: parseFloat(fileUploadCost),
      },
    });

    if (result.success) {
      setShowEditBotModal(false);
      resetBotForm();
      Alert.alert('Thành công', 'Bot đã được cập nhật!');
    } else {
      Alert.alert('Lỗi', result.error || 'Không thể cập nhật bot');
    }
  };

  const handleDeleteBot = (bot: Bot) => {
    Alert.alert(
      'Xác nhận xóa',
      `Bạn có chắc chắn muốn xóa bot "${bot.name}"?`,
      [
        { text: 'Hủy', style: 'cancel' },
        {
          text: 'Xóa',
          style: 'destructive',
          onPress: async () => {
            const result = await deleteBot(bot.id);
            if (result.success) {
              Alert.alert('Thành công', 'Bot đã được xóa');
            } else {
              Alert.alert('Lỗi', result.error || 'Không thể xóa bot');
            }
          },
        },
      ]
    );
  };

  const handleCreateCommand = async () => {
    if (!currentBot || !commandName.trim() || !commandResponse.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập đầy đủ thông tin');
      return;
    }

    const result = await createBotCommand({
      bot_id: currentBot.id,
      command: commandName.trim().startsWith('/') ? commandName.trim() : `/${commandName.trim()}`,
      description: commandDescription.trim(),
      response_type: commandType,
      response_content: commandResponse.trim(),
      price_per_use: parseFloat(commandPrice),
    });

    if (result.success) {
      setShowCommandModal(false);
      resetCommandForm();
      loadBotCommands(currentBot.id);
      Alert.alert('Thành công', 'Lệnh đã được tạo!');
    } else {
      Alert.alert('Lỗi', result.error || 'Không thể tạo lệnh');
    }
  };

  const handleEditCommand = async () => {
    if (!editingCommand || !commandName.trim() || !commandResponse.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập đầy đủ thông tin');
      return;
    }

    const result = await updateBotCommand(editingCommand.id, {
      command: commandName.trim().startsWith('/') ? commandName.trim() : `/${commandName.trim()}`,
      description: commandDescription.trim(),
      response_type: commandType,
      response_content: commandResponse.trim(),
      price_per_use: parseFloat(commandPrice),
      is_active: commandActive,
    });

    if (result.success) {
      setShowCommandModal(false);
      resetCommandForm();
      if (currentBot) {
        loadBotCommands(currentBot.id);
      }
      Alert.alert('Thành công', 'Lệnh đã được cập nhật!');
    } else {
      Alert.alert('Lỗi', result.error || 'Không thể cập nhật lệnh');
    }
  };

  const openEditBotModal = (bot: Bot) => {
    setCurrentBot(bot);
    setBotName(bot.name);
    setBotDescription(bot.description);
    setMessageCost(bot.pricing.message_cost.toString());
    setCommandCost(bot.pricing.command_cost.toString());
    setFileUploadCost(bot.pricing.file_upload_cost.toString());
    setShowEditBotModal(true);
  };

  const openCommandModal = (bot: Bot, command?: BotCommand) => {
    setCurrentBot(bot);
    loadBotCommands(bot.id);
    
    if (command) {
      setEditingCommand(command);
      setCommandName(command.command.replace('/', ''));
      setCommandDescription(command.description);
      setCommandResponse(command.response_content);
      setCommandPrice(command.price_per_use.toString());
      setCommandType(command.response_type);
      setCommandActive(command.is_active);
    } else {
      resetCommandForm();
    }
    
    setShowCommandModal(true);
  };

  const renderBotItem = ({ item }: { item: Bot }) => (
    <View style={styles.botCard}>
      <View style={styles.botHeader}>
        <View style={styles.botAvatar}>
          <Ionicons name="robot" size={24} color="#007AFF" />
        </View>
        
        <View style={styles.botInfo}>
          <Text style={styles.botName}>{item.name}</Text>
          <Text style={styles.botDescription} numberOfLines={2}>
            {item.description}
          </Text>
          <Text style={styles.botToken} numberOfLines={1}>
            Token: {item.token}
          </Text>
        </View>
        
        <View style={styles.botActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => openEditBotModal(item)}
          >
            <Ionicons name="create" size={20} color="#007AFF" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => openCommandModal(item)}
          >
            <Ionicons name="code" size={20} color="#34C759" />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteBot(item)}
          >
            <Ionicons name="trash" size={20} color="#FF3B30" />
          </TouchableOpacity>
        </View>
      </View>
      
      <View style={styles.botPricing}>
        <Text style={styles.pricingText}>
          Tin nhắn: {item.pricing.message_cost}đ | 
          Lệnh: {item.pricing.command_cost}đ | 
          File: {item.pricing.file_upload_cost}đ
        </Text>
      </View>
      
      <View style={styles.botStatus}>
        <View style={[
          styles.statusIndicator, 
          { backgroundColor: item.is_active ? '#34C759' : '#FF3B30' }
        ]} />
        <Text style={styles.statusText}>
          {item.is_active ? 'Hoạt động' : 'Tạm dừng'}
        </Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Quản lý Bot</Text>
        
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowCreateBotModal(true)}
        >
          <Ionicons name="add" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Bot List */}
      <FlatList
        data={userBots}
        renderItem={renderBotItem}
        keyExtractor={(item) => item.id}
        style={styles.botList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="robot-outline" size={64} color="#ccc" />
            <Text style={styles.emptyText}>Chưa có bot nào</Text>
            <Text style={styles.emptySubtext}>
              Tạo bot đầu tiên để bắt đầu
            </Text>
          </View>
        }
      />

      {/* Create Bot Modal */}
      <Modal
        visible={showCreateBotModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowCreateBotModal(false)}>
              <Text style={styles.modalCancel}>Hủy</Text>
            </TouchableOpacity>
            
            <Text style={styles.modalTitle}>Tạo Bot Mới</Text>
            
            <TouchableOpacity onPress={handleCreateBot}>
              <Text style={styles.modalSave}>Tạo</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Tên Bot *</Text>
              <TextInput
                style={styles.formInput}
                value={botName}
                onChangeText={setBotName}
                placeholder="Nhập tên bot"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Mô tả *</Text>
              <TextInput
                style={[styles.formInput, styles.textArea]}
                value={botDescription}
                onChangeText={setBotDescription}
                placeholder="Mô tả chức năng của bot"
                multiline
                numberOfLines={3}
              />
            </View>

            <Text style={styles.sectionTitle}>Bảng giá</Text>
            
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Giá tin nhắn (đ)</Text>
              <TextInput
                style={styles.formInput}
                value={messageCost}
                onChangeText={setMessageCost}
                placeholder="0.01"
                keyboardType="numeric"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Giá lệnh (đ)</Text>
              <TextInput
                style={styles.formInput}
                value={commandCost}
                onChangeText={setCommandCost}
                placeholder="0.05"
                keyboardType="numeric"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Giá upload file (đ)</Text>
              <TextInput
                style={styles.formInput}
                value={fileUploadCost}
                onChangeText={setFileUploadCost}
                placeholder="0.1"
                keyboardType="numeric"
              />
            </View>
          </ScrollView>
        </View>
      </Modal>

      {/* Edit Bot Modal - Similar structure to Create Bot Modal */}
      {/* Command Management Modal - Will be added in next part */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    padding: 5,
  },
  botList: {
    flex: 1,
    padding: 15,
  },
  botCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  botHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  botAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  botInfo: {
    flex: 1,
  },
  botName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  botDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  botToken: {
    fontSize: 12,
    color: '#999',
    fontFamily: 'monospace',
  },
  botActions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: 10,
    padding: 5,
  },
  botPricing: {
    marginBottom: 10,
  },
  pricingText: {
    fontSize: 12,
    color: '#666',
  },
  botStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 12,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 18,
    color: '#999',
    marginTop: 20,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#ccc',
    marginTop: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalCancel: {
    fontSize: 16,
    color: '#007AFF',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalSave: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    marginTop: 10,
  },
});
