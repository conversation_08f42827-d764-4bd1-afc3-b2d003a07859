import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, View, ActivityIndicator } from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { useAuthStore } from './src/store/authStore';
import { Chat } from './src/types';

// Screens
import AuthScreen from './src/screens/AuthScreen';
import ChatListScreen from './src/screens/ChatListScreen';
import ChatScreen from './src/screens/ChatScreen';
import BotManagementScreen from './src/screens/BotManagementScreen';
import ProfileScreen from './src/screens/ProfileScreen';

type Screen = 'auth' | 'chatList' | 'chat' | 'botManagement' | 'profile';

export default function App() {
  const { isAuthenticated, isLoading, checkAuthStatus } = useAuthStore();
  const [currentScreen, setCurrentScreen] = useState<Screen>('chatList');
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const handleChatSelect = (chat: Chat) => {
    setSelectedChat(chat);
    setCurrentScreen('chat');
  };

  const handleBackToList = () => {
    setSelectedChat(null);
    setCurrentScreen('chatList');
  };

  const handleNavigateToProfile = () => {
    setCurrentScreen('profile');
  };

  const handleNavigateToBotManagement = () => {
    setCurrentScreen('botManagement');
  };

  const handleBackFromProfile = () => {
    setCurrentScreen('chatList');
  };

  const handleBackFromBotManagement = () => {
    setCurrentScreen('chatList');
  };

  if (isLoading) {
    return (
      <SafeAreaProvider>
        <SafeAreaView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
        </SafeAreaView>
      </SafeAreaProvider>
    );
  }

  if (!isAuthenticated) {
    return (
      <SafeAreaProvider>
        <SafeAreaView style={styles.container}>
          <AuthScreen />
          <StatusBar style="auto" />
        </SafeAreaView>
      </SafeAreaProvider>
    );
  }

  const renderCurrentScreen = () => {
    switch (currentScreen) {
      case 'chat':
        return selectedChat ? (
          <ChatScreen
            chat={selectedChat}
            onBack={handleBackToList}
          />
        ) : null;

      case 'botManagement':
        return (
          <BotManagementScreen
            onBack={handleBackFromBotManagement}
          />
        );

      case 'profile':
        return (
          <ProfileScreen
            onBack={handleBackFromProfile}
          />
        );

      default:
        return (
          <ChatListScreen
            onChatSelect={handleChatSelect}
            onNavigateToProfile={handleNavigateToProfile}
            onNavigateToBotManagement={handleNavigateToBotManagement}
          />
        );
    }
  };

  return (
    <SafeAreaProvider>
      <SafeAreaView style={styles.container}>
        {renderCurrentScreen()}
        <StatusBar style="auto" />
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});
