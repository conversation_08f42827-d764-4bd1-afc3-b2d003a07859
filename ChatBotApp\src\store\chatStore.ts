import { create } from 'zustand';
import { Chat, Message, GiftedChatMessage } from '../types';
import { supabase, TABLES, subscribeToMessages } from '../config/supabase';
import { v4 as uuidv4 } from 'react-native-uuid';

interface ChatState {
  chats: Chat[];
  currentChat: Chat | null;
  messages: { [chatId: string]: GiftedChatMessage[] };
  isLoading: boolean;
  
  // Actions
  loadChats: (userId: string) => Promise<void>;
  loadMessages: (chatId: string) => Promise<void>;
  sendMessage: (chatId: string, content: string, userId: string) => Promise<void>;
  createChat: (name: string, type: Chat['type'], participants: string[], botId?: string) => Promise<Chat | null>;
  setCurrentChat: (chat: Chat | null) => void;
  subscribeToChat: (chatId: string) => () => void;
  executeBotCommand: (chatId: string, command: string, userId: string) => Promise<void>;
}

export const useChatStore = create<ChatState>((set, get) => ({
  chats: [],
  currentChat: null,
  messages: {},
  isLoading: false,

  loadChats: async (userId: string) => {
    try {
      set({ isLoading: true });

      // Lấy danh sách chat mà user tham gia
      const { data: chatParticipants, error: participantsError } = await supabase
        .from('chat_participants')
        .select(`
          chat_id,
          chats (
            id,
            name,
            type,
            bot_id,
            created_by,
            created_at,
            updated_at
          )
        `)
        .eq('user_id', userId);

      if (participantsError) {
        throw participantsError;
      }

      const chats = chatParticipants?.map(cp => cp.chats).filter(Boolean) || [];

      // Lấy tin nhắn cuối cùng cho mỗi chat
      const chatsWithLastMessage = await Promise.all(
        chats.map(async (chat) => {
          const { data: lastMessage } = await supabase
            .from(TABLES.MESSAGES)
            .select('*')
            .eq('chat_id', chat.id)
            .order('created_at', { ascending: false })
            .limit(1)
            .single();

          return {
            ...chat,
            participants: [], // Sẽ load sau nếu cần
            last_message: lastMessage,
          };
        })
      );

      set({ chats: chatsWithLastMessage, isLoading: false });
    } catch (error) {
      console.error('Load chats error:', error);
      set({ isLoading: false });
    }
  },

  loadMessages: async (chatId: string) => {
    try {
      const { data: messages, error } = await supabase
        .from(TABLES.MESSAGES)
        .select(`
          *,
          sender:users(id, username, avatar)
        `)
        .eq('chat_id', chatId)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        throw error;
      }

      // Convert to GiftedChat format
      const giftedMessages: GiftedChatMessage[] = messages?.map(msg => ({
        _id: msg.id,
        text: msg.content,
        createdAt: new Date(msg.created_at),
        user: {
          _id: msg.sender_id,
          name: msg.sender?.username || 'Unknown',
          avatar: msg.sender?.avatar,
        },
        image: msg.message_type === 'image' ? msg.file_url : undefined,
        system: msg.message_type === 'system',
      })) || [];

      set(state => ({
        messages: {
          ...state.messages,
          [chatId]: giftedMessages,
        },
      }));
    } catch (error) {
      console.error('Load messages error:', error);
    }
  },

  sendMessage: async (chatId: string, content: string, userId: string) => {
    try {
      const messageId = uuidv4();
      const now = new Date().toISOString();

      // Thêm message vào database
      const { error } = await supabase
        .from(TABLES.MESSAGES)
        .insert({
          id: messageId,
          chat_id: chatId,
          sender_id: userId,
          sender_type: 'user',
          content,
          message_type: 'text',
          created_at: now,
          updated_at: now,
        });

      if (error) {
        throw error;
      }

      // Cập nhật local state ngay lập tức
      const { messages } = get();
      const chatMessages = messages[chatId] || [];
      
      const newMessage: GiftedChatMessage = {
        _id: messageId,
        text: content,
        createdAt: new Date(now),
        user: {
          _id: userId,
          name: 'You',
        },
      };

      set(state => ({
        messages: {
          ...state.messages,
          [chatId]: [newMessage, ...chatMessages],
        },
      }));

      // Kiểm tra xem có phải chat với bot không
      const { currentChat } = get();
      if (currentChat?.type === 'bot' && currentChat.bot_id) {
        // Xử lý response từ bot
        await get().executeBotCommand(chatId, content, userId);
      }
    } catch (error) {
      console.error('Send message error:', error);
    }
  },

  createChat: async (name: string, type: Chat['type'], participants: string[], botId?: string) => {
    try {
      const chatId = uuidv4();
      const now = new Date().toISOString();

      // Tạo chat
      const { data: chat, error: chatError } = await supabase
        .from(TABLES.CHATS)
        .insert({
          id: chatId,
          name,
          type,
          bot_id: botId,
          created_by: participants[0], // Người đầu tiên là creator
          created_at: now,
          updated_at: now,
        })
        .select()
        .single();

      if (chatError) {
        throw chatError;
      }

      // Thêm participants
      const participantInserts = participants.map(userId => ({
        chat_id: chatId,
        user_id: userId,
        joined_at: now,
      }));

      const { error: participantsError } = await supabase
        .from('chat_participants')
        .insert(participantInserts);

      if (participantsError) {
        throw participantsError;
      }

      const newChat: Chat = {
        ...chat,
        participants,
      };

      set(state => ({
        chats: [newChat, ...state.chats],
      }));

      return newChat;
    } catch (error) {
      console.error('Create chat error:', error);
      return null;
    }
  },

  setCurrentChat: (chat: Chat | null) => {
    set({ currentChat: chat });
  },

  subscribeToChat: (chatId: string) => {
    const subscription = subscribeToMessages(chatId, (payload) => {
      if (payload.eventType === 'INSERT') {
        const newMessage = payload.new;
        
        // Convert to GiftedChat format
        const giftedMessage: GiftedChatMessage = {
          _id: newMessage.id,
          text: newMessage.content,
          createdAt: new Date(newMessage.created_at),
          user: {
            _id: newMessage.sender_id,
            name: newMessage.sender_type === 'bot' ? 'Bot' : 'User',
          },
          system: newMessage.message_type === 'system',
        };

        set(state => {
          const chatMessages = state.messages[chatId] || [];
          return {
            messages: {
              ...state.messages,
              [chatId]: [giftedMessage, ...chatMessages],
            },
          };
        });
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  },

  executeBotCommand: async (chatId: string, command: string, userId: string) => {
    try {
      // Tìm bot command tương ứng
      const { currentChat } = get();
      if (!currentChat?.bot_id) return;

      const { data: botCommands, error } = await supabase
        .from('bot_commands')
        .select('*')
        .eq('bot_id', currentChat.bot_id)
        .eq('is_active', true);

      if (error) {
        throw error;
      }

      // Tìm command phù hợp
      const matchedCommand = botCommands?.find(cmd => 
        command.toLowerCase().startsWith(cmd.command.toLowerCase())
      );

      if (matchedCommand) {
        // Kiểm tra balance của user
        const { data: user } = await supabase
          .from('users')
          .select('balance')
          .eq('id', userId)
          .single();

        if (!user || user.balance < matchedCommand.price_per_use) {
          // Gửi thông báo không đủ tiền
          await supabase
            .from(TABLES.MESSAGES)
            .insert({
              id: uuidv4(),
              chat_id: chatId,
              sender_id: currentChat.bot_id,
              sender_type: 'bot',
              content: 'Bạn không đủ tiền để sử dụng lệnh này. Vui lòng nạp thêm tiền.',
              message_type: 'system',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            });
          return;
        }

        // Trừ tiền và gửi response
        await supabase
          .from('users')
          .update({ balance: user.balance - matchedCommand.price_per_use })
          .eq('id', userId);

        // Ghi lại transaction
        await supabase
          .from('transactions')
          .insert({
            id: uuidv4(),
            user_id: userId,
            type: 'bot_usage',
            amount: -matchedCommand.price_per_use,
            description: `Sử dụng lệnh ${matchedCommand.command}`,
            bot_id: currentChat.bot_id,
            status: 'completed',
            created_at: new Date().toISOString(),
          });

        // Gửi response từ bot
        await supabase
          .from(TABLES.MESSAGES)
          .insert({
            id: uuidv4(),
            chat_id: chatId,
            sender_id: currentChat.bot_id,
            sender_type: 'bot',
            content: matchedCommand.response_content,
            message_type: matchedCommand.response_type,
            cost: matchedCommand.price_per_use,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
      } else {
        // Gửi thông báo không tìm thấy lệnh
        await supabase
          .from(TABLES.MESSAGES)
          .insert({
            id: uuidv4(),
            chat_id: chatId,
            sender_id: currentChat.bot_id,
            sender_type: 'bot',
            content: 'Lệnh không được tìm thấy. Gõ /help để xem danh sách lệnh.',
            message_type: 'text',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
      }
    } catch (error) {
      console.error('Execute bot command error:', error);
    }
  },
}));
