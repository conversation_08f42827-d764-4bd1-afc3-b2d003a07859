import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  ScrollView,
} from 'react-native';
import { GiftedChat, IMessage, InputToolbar, Send } from 'react-native-gifted-chat';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { useAuthStore } from '../store/authStore';
import { useChatStore } from '../store/chatStore';
import { useBotStore } from '../store/botStore';
import { Chat, GiftedChatMessage } from '../types';

interface ChatScreenProps {
  chat: Chat;
  onBack: () => void;
}

export default function ChatScreen({ chat, onBack }: ChatScreenProps) {
  const { user } = useAuthStore();
  const { 
    messages, 
    loadMessages, 
    sendMessage, 
    subscribeToChat,
    setCurrentChat 
  } = useChatStore();
  const { executeCode } = useBotStore();

  const [showCodeModal, setShowCodeModal] = useState(false);
  const [code, setCode] = useState('');
  const [language, setLanguage] = useState('javascript');
  const [isExecuting, setIsExecuting] = useState(false);

  const chatMessages = messages[chat.id] || [];

  useEffect(() => {
    setCurrentChat(chat);
    loadMessages(chat.id);

    // Subscribe to real-time messages
    const unsubscribe = subscribeToChat(chat.id);
    
    return () => {
      unsubscribe();
      setCurrentChat(null);
    };
  }, [chat.id]);

  const onSend = useCallback((newMessages: IMessage[] = []) => {
    if (!user) return;

    const message = newMessages[0];
    if (message?.text) {
      sendMessage(chat.id, message.text, user.id);
    }
  }, [chat.id, user, sendMessage]);

  const handleImagePicker = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        // TODO: Upload image to storage and send message with image URL
        Alert.alert('Thông báo', 'Tính năng upload ảnh sẽ được cập nhật trong phiên bản tiếp theo');
      }
    } catch (error) {
      Alert.alert('Lỗi', 'Không thể chọn ảnh');
    }
  };

  const handleDocumentPicker = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        // TODO: Upload file to storage and send message with file URL
        Alert.alert('Thông báo', 'Tính năng upload file sẽ được cập nhật trong phiên bản tiếp theo');
      }
    } catch (error) {
      Alert.alert('Lỗi', 'Không thể chọn file');
    }
  };

  const handleExecuteCode = async () => {
    if (!user || !code.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập code');
      return;
    }

    if (chat.type !== 'bot' || !chat.bot_id) {
      Alert.alert('Lỗi', 'Chỉ có thể thực thi code trong chat với bot');
      return;
    }

    setIsExecuting(true);
    
    try {
      const result = await executeCode(code, language, chat.bot_id, user.id);
      
      if (result.success) {
        // Gửi code và kết quả như tin nhắn
        await sendMessage(chat.id, `\`\`\`${language}\n${code}\n\`\`\``, user.id);
        
        if (result.output) {
          // Bot sẽ tự động gửi kết quả thông qua webhook/subscription
          setTimeout(() => {
            sendMessage(chat.id, `**Kết quả thực thi:**\n\`\`\`\n${result.output}\n\`\`\``, chat.bot_id!);
          }, 1000);
        }
        
        setShowCodeModal(false);
        setCode('');
      } else {
        Alert.alert('Lỗi thực thi', result.error || 'Có lỗi xảy ra');
      }
    } catch (error) {
      Alert.alert('Lỗi', 'Không thể thực thi code');
    } finally {
      setIsExecuting(false);
    }
  };

  const renderInputToolbar = (props: any) => (
    <View style={styles.inputContainer}>
      <View style={styles.attachmentButtons}>
        <TouchableOpacity 
          style={styles.attachmentButton}
          onPress={handleImagePicker}
        >
          <Ionicons name="image" size={24} color="#007AFF" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.attachmentButton}
          onPress={handleDocumentPicker}
        >
          <Ionicons name="document" size={24} color="#007AFF" />
        </TouchableOpacity>
        
        {chat.type === 'bot' && (
          <TouchableOpacity 
            style={styles.attachmentButton}
            onPress={() => setShowCodeModal(true)}
          >
            <Ionicons name="code" size={24} color="#007AFF" />
          </TouchableOpacity>
        )}
      </View>
      
      <InputToolbar
        {...props}
        containerStyle={styles.inputToolbar}
        primaryStyle={styles.inputPrimary}
      />
    </View>
  );

  const renderSend = (props: any) => (
    <Send {...props} containerStyle={styles.sendContainer}>
      <Ionicons name="send" size={24} color="#007AFF" />
    </Send>
  );

  const languages = [
    { label: 'JavaScript', value: 'javascript' },
    { label: 'Python', value: 'python' },
    { label: 'Java', value: 'java' },
    { label: 'C++', value: 'cpp' },
    { label: 'C#', value: 'csharp' },
    { label: 'Go', value: 'go' },
    { label: 'Rust', value: 'rust' },
  ];

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>{chat.name}</Text>
          <Text style={styles.headerSubtitle}>
            {chat.type === 'bot' ? 'Bot Chat' : 'Direct Chat'}
          </Text>
        </View>
        
        <TouchableOpacity style={styles.headerButton}>
          <Ionicons name="information-circle" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Chat */}
      <GiftedChat
        messages={chatMessages}
        onSend={onSend}
        user={{
          _id: user?.id || '',
          name: user?.username || '',
          avatar: user?.avatar,
        }}
        renderInputToolbar={renderInputToolbar}
        renderSend={renderSend}
        placeholder="Nhập tin nhắn..."
        alwaysShowSend
        scrollToBottom
        scrollToBottomComponent={() => (
          <Ionicons name="chevron-down" size={20} color="#007AFF" />
        )}
      />

      {/* Code Execution Modal */}
      <Modal
        visible={showCodeModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View style={styles.codeModalContainer}>
          <View style={styles.codeModalHeader}>
            <TouchableOpacity onPress={() => setShowCodeModal(false)}>
              <Text style={styles.codeModalCancel}>Hủy</Text>
            </TouchableOpacity>
            
            <Text style={styles.codeModalTitle}>Thực thi Code</Text>
            
            <TouchableOpacity 
              onPress={handleExecuteCode}
              disabled={isExecuting}
            >
              <Text style={[
                styles.codeModalExecute,
                isExecuting && styles.codeModalExecuteDisabled
              ]}>
                {isExecuting ? 'Đang chạy...' : 'Chạy'}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.languageSelector}>
            <Text style={styles.languageLabel}>Ngôn ngữ:</Text>
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              style={styles.languageList}
            >
              {languages.map((lang) => (
                <TouchableOpacity
                  key={lang.value}
                  style={[
                    styles.languageButton,
                    language === lang.value && styles.languageButtonActive
                  ]}
                  onPress={() => setLanguage(lang.value)}
                >
                  <Text style={[
                    styles.languageButtonText,
                    language === lang.value && styles.languageButtonTextActive
                  ]}>
                    {lang.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          <TextInput
            style={styles.codeInput}
            value={code}
            onChangeText={setCode}
            placeholder={`Nhập code ${language} tại đây...`}
            multiline
            textAlignVertical="top"
            autoCapitalize="none"
            autoCorrect={false}
            fontFamily="monospace"
          />

          <Text style={styles.codeNote}>
            💡 Lưu ý: Việc thực thi code sẽ tốn phí. Kiểm tra số dư trước khi chạy.
          </Text>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    marginRight: 15,
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  headerButton: {
    marginLeft: 15,
  },
  inputContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 10,
    paddingTop: 10,
  },
  attachmentButtons: {
    flexDirection: 'row',
    paddingBottom: 10,
  },
  attachmentButton: {
    marginRight: 15,
    padding: 5,
  },
  inputToolbar: {
    backgroundColor: 'transparent',
    borderTopWidth: 0,
  },
  inputPrimary: {
    alignItems: 'center',
  },
  sendContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    marginBottom: 5,
  },
  codeModalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  codeModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  codeModalCancel: {
    fontSize: 16,
    color: '#007AFF',
  },
  codeModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  codeModalExecute: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: 'bold',
  },
  codeModalExecuteDisabled: {
    color: '#ccc',
  },
  languageSelector: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  languageLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  languageList: {
    flexDirection: 'row',
  },
  languageButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 10,
  },
  languageButtonActive: {
    backgroundColor: '#007AFF',
  },
  languageButtonText: {
    fontSize: 14,
    color: '#333',
  },
  languageButtonTextActive: {
    color: 'white',
  },
  codeInput: {
    flex: 1,
    margin: 20,
    padding: 15,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    fontSize: 14,
    backgroundColor: '#f9f9f9',
  },
  codeNote: {
    padding: 20,
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
});
